import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Sidebar.css';

const Sidebar = () => {
  const location = useLocation();

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2>PokeMates</h2>
        <span className="poke-elements">⚡🔥💧🌿</span>
      </div>
      
      <nav className="sidebar-nav">
        <Link 
          to="/" 
          className={`nav-item ${location.pathname === '/' ? 'active' : ''}`}
        >
          <span className="nav-icon">🏠</span>
          Home
        </Link>
        
        <Link 
          to="/pokemons" 
          className={`nav-item ${location.pathname === '/pokemons' ? 'active' : ''}`}
        >
          <span className="nav-icon">👁️</span>
          All Pokemons
        </Link>
        
        <Link 
          to="/new" 
          className={`nav-item ${location.pathname === '/new' ? 'active' : ''}`}
        >
          <span className="nav-icon">➕</span>
          Add Pokemon
        </Link>
      </nav>
    </div>
  );
};

export default Sidebar; 