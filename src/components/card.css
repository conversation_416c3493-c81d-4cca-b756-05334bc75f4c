.Card {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin: 15px;
  transition: box-shadow 0.2s ease, transform 0.2s ease;
  max-width: 280px;
  color: #333;
  cursor: pointer;
}

.Card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #999;
  transform: translateY(-2px);
}

.card-link {
  text-decoration: none;
  color: inherit;
  display: block;
  margin-bottom: 15px;
}

.card-link:hover {
  text-decoration: none;
  color: inherit;
}

.pokemon-image {
  text-align: center;
  margin-bottom: 15px;
}

.pokemon-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  padding: 8px;
  transition: transform 0.2s ease;
}

.Card:hover .pokemon-placeholder {
  transform: scale(1.05);
}

.card-content {
  text-align: center;
}

.pokemon-name {
  font-size: 1.2em;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  text-transform: capitalize;
}

.pokemon-details {
  margin-bottom: 16px;
}

.pokemon-type,
.pokemon-generation {
  margin: 6px 0;
  font-size: 14px;
  color: #666;
}

.label {
  font-weight: 500;
  color: #333;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 15px;
}

.edit-btn,
.delete-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background-color: #fff;
  color: #333;
}

.edit-btn:hover {
  background-color: #f8f9fa;
  border-color: #999;
}

.delete-btn:hover {
  background-color: #f8f9fa;
  border-color: #999;
  color: #d32f2f;
}


.card-actions a {
  text-decoration: none;
}