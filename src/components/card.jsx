import "./card.css";
import { <PERSON> } from "react-router-dom";
import { supabase } from "../client";
import pokemonImage from "../assets/generic-pokemon-shilouette.png";

const Card = (props) => {
  const deletePokemon = async () => {
    if (window.confirm('Are you sure you want to delete this Pokemon?')) {
      await supabase
        .from("Posts")
        .delete()
        .eq("id", props.id);
      window.location.reload();
    }
  };

  return (
    <div className="Card">
      <Link to={`/pokemon/${props.id}`} className="card-link">
        <div className="pokemon-image">
          <img 
            src={pokemonImage}
            alt="Pokemon placeholder"
            className="pokemon-placeholder"
          />
        </div>
        
        <div className="card-content">
          <h2 className="pokemon-name">{props.Name}</h2>
          <div className="pokemon-details">
            <p className="pokemon-type">
              <span className="label">Type:</span> {props.Type}
            </p>
            <p className="pokemon-generation">
              <span className="label">Generation:</span> {props.Generation}
            </p>
          </div>
        </div>
      </Link>
      
      <div className="card-actions">
        <Link to={`/edit/${props.id}`}>
          <button className="edit-btn">
            ✏️ Edit
          </button>
        </Link>
        <button className="delete-btn" onClick={deletePokemon}>
          🗑️ Delete
        </button>
      </div>
    </div>
  );
};

export default Card;
