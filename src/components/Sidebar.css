.sidebar {
  width: 260px;
  min-height: 100vh;
  background-color: #ffffff;
  border-right: 1px solid #e0e0e0;
  padding: 20px;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-sizing: border-box;
  overflow: hidden; 
}

.sidebar-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.sidebar-header h2 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 1.3em;
  font-weight: 600;
  word-wrap: break-word;
}

.poke-elements {
  font-size: 1.2em;
  display: block;
  color: #666;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  color: #666;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 13px;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-item:hover {
  background-color: #f8f9fa;
  color: #333;
}

.nav-item.active {
  background-color: #333;
  color: white;
}

.nav-icon {
  font-size: 14px;
  width: 18px;
  text-align: center;
  flex-shrink: 0; 
}


@media (max-width: 768px) {
  .sidebar {
    position: relative;
    width: 100%;
    min-height: auto;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .nav-item {
    white-space: normal; 
  }
} 