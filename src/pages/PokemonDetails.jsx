import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { supabase } from "../client";
import { getTypeEffectiveness } from "../utils/typeEffectiveness";
import pokemonImage from "../assets/generic-pokemon-shilouette.png";
import "./PokemonDetails.css";

const PokemonDetails = () => {
  const { id } = useParams();
  const [pokemon, setPokemon] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPokemon();
  }, [id]);

  const fetchPokemon = async () => {
    try {
      const { data, error } = await supabase
        .from("Posts")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching Pokemon:", error);
      } else {
        setPokemon(data);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <h2>Loading Pokemon details...</h2>
      </div>
    );
  }

  if (!pokemon) {
    return (
      <div className="error-container">
        <h2>Pokemon not found</h2>
        <Link to="/pokemons">
          <button>Back to All Pokemon</button>
        </Link>
      </div>
    );
  }

  const typeData = getTypeEffectiveness(pokemon.Type);
  const strongAgainstText = typeData.strongAgainst.length > 0 
    ? typeData.strongAgainst.join(', ') 
    : 'none';
  const weakAgainstText = typeData.weakAgainst.length > 0 
    ? typeData.weakAgainst.join(', ') 
    : 'none';

  return (
    <div className="pokemon-details-container">
      <div className="pokemon-details-card">
        <div className="pokemon-details-header">
          <Link to="/pokemons" className="back-link">
            ← Back to All Pokemon
          </Link>
          <Link to={`/edit/${pokemon.id}`} className="edit-link">
            Edit Pokemon
          </Link>
        </div>

        <div className="pokemon-details-content">
          <div className="pokemon-details-image">
            <img 
              src={pokemonImage}
              alt={pokemon.Name}
              className="details-pokemon-image"
            />
          </div>

          <div className="pokemon-info">
            <h1 className="pokemon-details-name">{pokemon.Name}</h1>
            
            <div className="pokemon-stats">
              <div className="stat-item">
                <span className="stat-label">Type:</span>
                <span className="stat-value type-badge">{pokemon.Type}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Generation:</span>
                <span className="stat-value">{pokemon.Generation}</span>
              </div>
            </div>

            <div className="type-effectiveness">
              <h3>Type Effectiveness</h3>
              <div className="effectiveness-text">
                <p>
                  Your Pokemon <strong>{pokemon.Name}</strong> being type <strong>{pokemon.Type}</strong> is strong against <strong>{strongAgainstText}</strong>.
                </p>
                <p>
                  Your Pokemon <strong>{pokemon.Name}</strong> being type <strong>{pokemon.Type}</strong> is weak against <strong>{weakAgainstText}</strong>.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PokemonDetails; 