import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import "./editPokemon.css";
import { supabase } from "../client";
import pokemonImage from "../assets/generic-pokemon-shilouette.png";

const EditPokemon = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [post, setPost] = useState({
    id: null,
    Name: "",
    Type: "",
    Generation: "",
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPost();
  }, [id]);

  const fetchPost = async () => {
    const { data } = await supabase
      .from("Posts")
      .select()
      .eq("id", id)
      .single();
    
    if (data) {
      setPost(data);
    }
    setLoading(false);
  };

  const updatePost = async (event) => {
    event.preventDefault();

    await supabase
      .from("Posts")
      .update({
        Name: post.Name,
        Type: post.Type,
        Generation: post.Generation,
      })
      .eq("id", id);

    navigate("/pokemons");
  };

  const handleChange = (event) => {
    const { name, value } = event.target;
    setPost((prev) => {
      return {
        ...prev,
        [name]: value,
      };
    });
  };

  const deletePokemon = async (event) => {
    event.preventDefault();

    if (window.confirm('Are you sure you want to delete this Pokemon?')) {
      await supabase.from("Posts").delete().eq("id", id);
      navigate("/pokemons");
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <h2>Loading Pokemon data...</h2>
      </div>
    );
  }

  return (
    <div className="edit-pokemon-container">
      <div className="edit-form-wrapper">
        <h1>Edit Pokemon</h1>
        <div className="pokemon-preview">
          <img 
            src={pokemonImage}
            alt="Pokemon"
            className="edit-pokemon-image"
          />
        </div>
        
        <form className="edit-form">
          <div className="form-group">
            <label htmlFor="Name">Pokemon Name</label>
            <input
              type="text"
              id="Name"
              name="Name"
              value={post.Name}
              onChange={handleChange}
              placeholder="Enter Pokemon name"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="Type">Type</label>
            <input
              type="text"
              id="Type"
              name="Type"
              value={post.Type}
              onChange={handleChange}
              placeholder="e.g., Electric, Fire, Water"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="Generation">Generation</label>
            <input
              type="number"
              id="Generation"
              name="Generation"
              value={post.Generation}
              onChange={handleChange}
              placeholder="1, 2, 3, etc."
              min="1"
              max="9"
              required
            />
          </div>

          <div className="form-actions">
            <button type="submit" className="update-btn" onClick={updatePost}>
              ✅ Update Pokemon
            </button>
            <button type="button" className="delete-btn" onClick={deletePokemon}>
              🗑️ Delete Pokemon
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditPokemon;
