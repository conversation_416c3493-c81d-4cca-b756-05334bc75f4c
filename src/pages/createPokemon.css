.create-pokemon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 20px;
}

.create-form-wrapper {
  background: white;
  border: 1px solid #e0e0e0;
  padding: 40px;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
  color: #333;
}

.create-form-wrapper h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8em;
  font-weight: 600;
}

.pokemon-preview {
  text-align: center;
  margin-bottom: 30px;
}

.create-pokemon-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  padding: 8px;
  margin-bottom: 15px;
}

.pokemon-preview p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  color: #333;
}

.form-group input:focus {
  outline: none;
  border-color: #999;
}

.form-group input::placeholder {
  color: #999;
}

.create-btn {
  padding: 12px 20px;
  border: 1px solid #333;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #333;
  color: white;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.create-btn:hover:not(:disabled) {
  background-color: #555;
  border-color: #555;
}

.create-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}


@media (max-width: 768px) {
  .create-form-wrapper {
    padding: 20px;
    margin: 10px;
  }
  
  .create-form-wrapper h1 {
    font-size: 1.5em;
  }
  
  .create-pokemon-image {
    width: 60px;
    height: 60px;
  }
}