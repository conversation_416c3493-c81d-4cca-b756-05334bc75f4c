.edit-pokemon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 20px;
}

.edit-form-wrapper {
  background: white;
  border: 1px solid #e0e0e0;
  padding: 40px;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
  color: #333;
}

.edit-form-wrapper h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 1.8em;
  font-weight: 600;
}

.pokemon-preview {
  text-align: center;
  margin-bottom: 30px;
}

.edit-pokemon-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  padding: 8px;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  color: #333;
}

.form-group input:focus {
  outline: none;
  border-color: #999;
}

.form-group input::placeholder {
  color: #999;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.update-btn,
.delete-btn {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.update-btn {
  background-color: #333;
  border-color: #333;
  color: white;
}

.update-btn:hover {
  background-color: #555;
  border-color: #555;
}

.delete-btn {
  background-color: white;
  border-color: #ddd;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #f8f9fa;
  border-color: #d32f2f;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  text-align: center;
}

.loading-container h2 {
  color: #666;
  font-size: 1.2em;
  font-weight: 400;
}


@media (max-width: 768px) {
  .edit-form-wrapper {
    padding: 20px;
    margin: 10px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .edit-form-wrapper h1 {
    font-size: 1.5em;
  }
}