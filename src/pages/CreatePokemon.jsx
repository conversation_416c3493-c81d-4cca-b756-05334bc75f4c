import { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./createPokemon.css";
import { supabase } from "../client";
import pokemonImage from "../assets/generic-pokemon-shilouette.png";

const CreatePokemon = () => {
  const navigate = useNavigate();
  const [post, setPost] = useState({ Name: "", Type: "", Generation: "" });
  const [loading, setLoading] = useState(false);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setPost((prev) => {
      return {
        ...prev,
        [name]: value,
      };
    });
  };

  const createPost = async (event) => {
    event.preventDefault();
    
    if (!post.Name || !post.Type || !post.Generation) {
      alert("Please fill in all fields!");
      return;
    }

    setLoading(true);
    
    try {
      console.log("Creating Pokemon:", post);
      
      const { data, error } = await supabase
        .from("Posts")
        .insert({
          Name: post.Name,
          Type: post.Type,
          Generation: parseInt(post.Generation), 
        })
        .select();

      if (error) {
        console.error("Error creating Pokemon:", error);
        alert("Error creating Pokemon: " + error.message);
        setLoading(false);
        return;
      }

      console.log("Pokemon created successfully:", data);
      
      setPost({ Name: "", Type: "", Generation: "" });
      
      setTimeout(() => {
        navigate("/pokemons");
      }, 500);
      
    } catch (error) {
      console.error("Unexpected error:", error);
      alert("Unexpected error occurred: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="create-pokemon-container">
      <div className="create-form-wrapper">
        <h1>Add New Pokemon</h1>
        <div className="pokemon-preview">
          <img 
            src={pokemonImage}
            alt="New Pokemon"
            className="create-pokemon-image"
          />
          <p>Add your favorite Pokemon to your collection!</p>
        </div>
        
        <form className="create-form" onSubmit={createPost}>
          <div className="form-group">
            <label htmlFor="Name">Pokemon Name</label>
            <input
              type="text"
              value={post.Name}
              id="Name"
              name="Name"
              onChange={handleChange}
              placeholder="Enter Pokemon name"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="Type">Type</label>
            <input
              type="text"
              value={post.Type}
              id="Type"
              name="Type"
              onChange={handleChange}
              placeholder="e.g., Electric, Fire, Water"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="Generation">Generation</label>
            <input
              type="number"
              value={post.Generation}
              id="Generation"
              name="Generation"
              onChange={handleChange}
              placeholder="1, 2, 3, etc."
              min="1"
              max="9"
              required
            />
          </div>

          <button 
            type="submit" 
            className="create-btn"
            disabled={loading}
          >
            {loading ? "Adding..." : "Add Pokemon"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default CreatePokemon;
