.pokemon-details-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.pokemon-details-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pokemon-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.back-link,
.edit-link {
  padding: 8px 16px;
  background-color: #333;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.back-link:hover,
.edit-link:hover {
  background-color: #555;
}

.pokemon-details-content {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.pokemon-details-image {
  flex-shrink: 0;
}

.details-pokemon-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  padding: 20px;
  object-fit: contain;
}

.pokemon-info {
  flex: 1;
}

.pokemon-details-name {
  font-size: 2.5em;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.pokemon-stats {
  margin-bottom: 30px;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.stat-label {
  font-weight: 500;
  color: #666;
  min-width: 100px;
  font-size: 16px;
}

.stat-value {
  color: #333;
  font-weight: 500;
  font-size: 16px;
}

.type-badge {
  background-color: #333;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  text-transform: capitalize;
}

.type-effectiveness {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.type-effectiveness h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.3em;
  font-weight: 600;
}

.effectiveness-text p {
  margin: 0 0 12px 0;
  line-height: 1.6;
  color: #555;
  font-size: 15px;
}

.effectiveness-text p:last-child {
  margin-bottom: 0;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  text-align: center;
}

.loading-container h2,
.error-container h2 {
  color: #666;
  font-size: 1.2em;
  font-weight: 400;
  margin-bottom: 20px;
}

.error-container button {
  background-color: #333;
  color: white;
  border: 1px solid #333;
  padding: 12px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-container button:hover {
  background-color: #555;
  border-color: #555;
}


@media (max-width: 768px) {
  .pokemon-details-container {
    padding: 15px;
  }
  
  .pokemon-details-card {
    padding: 20px;
  }
  
  .pokemon-details-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .pokemon-details-content {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }
  
  .details-pokemon-image {
    width: 150px;
    height: 150px;
  }
  
  .pokemon-details-name {
    font-size: 2em;
    text-align: center;
  }
} 