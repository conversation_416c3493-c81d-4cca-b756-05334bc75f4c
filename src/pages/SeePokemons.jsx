import { useState, useEffect } from "react";
import Card from "../components/card";
import { supabase } from "../client";
import { Link } from "react-router-dom";
import { useLocation } from "react-router-dom";

const SeePokemons = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  
  useEffect(() => {
    fetchPosts();
  }, [location.pathname]);


  useEffect(() => {
    const handleFocus = () => {
      fetchPosts();
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  
  const fetchPosts = async () => {
    try {
      setLoading(true);
      
      
      const { data, error } = await supabase
        .from("Posts")
        .select("*")
        .order("Name", { ascending: true });
      if (error) {
        setPosts([]);
      } else {
        setPosts(data || []);
      }
    } catch (error) {
      alert("Unexpected error loading Pokemon: " + error.message);
      setPosts([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <h2>Loading your PokeMates...</h2>
      </div>
    );
  }

  return (
    <div className="ReadPosts">
      <div style={{ marginBottom: '20px', textAlign: 'right' }}>
        <button 
          onClick={fetchPosts}
          style={{
            padding: '8px 16px',
            backgroundColor: '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh
        </button>
      </div>
      
      {posts && posts.length > 0 ? (
        <div className="pokemon-grid">
          {posts.map((post) => (
            <Card
              key={post.id}
              id={post.id}
              Name={post.Name}
              Type={post.Type}
              Generation={post.Generation}
            />
          ))}
        </div>
      ) : (
        <div className="no-pokemons">
          <h2>No PokeMates added to display 😔</h2>
          <p>You haven't added any Pokemons yet. Start building your collection!</p>
          <Link to="/new">
            <button className="add-pokemon-btn">
              Add Your First Pokemon
            </button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default SeePokemons;
