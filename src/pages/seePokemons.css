.ReadPosts {
  padding: 0;
  min-height: 80vh;
}

.pokemon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  text-align: center;
}

.loading-container h2 {
  color: #666;
  font-size: 1.2em;
  font-weight: 400;
}

.no-pokemons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  text-align: center;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 40px;
  margin: 20px 0;
}

.no-pokemons h2 {
  font-size: 1.5em;
  color: #333;
  margin-bottom: 12px;
  font-weight: 500;
}

.no-pokemons p {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

.add-pokemon-btn {
  background-color: #333;
  color: white;
  border: 1px solid #333;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.add-pokemon-btn:hover {
  background-color: #555;
  border-color: #555;
}


@media (max-width: 768px) {
  .pokemon-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .no-pokemons {
    margin: 10px 0;
    padding: 24px;
  }
  
  .no-pokemons h2 {
    font-size: 1.3em;
  }
}

