// Pokemon Type Effectiveness Data
export const typeEffectiveness = {
  Bug: {
    strongAgainst: ['Grass', 'Psychic', 'Dark'],
    weakAgainst: ['Fire', 'Fighting', 'Poison', 'Flying', 'Ghost', 'Steel']
  },
  Dark: {
    strongAgainst: ['<PERSON>', 'Psychic'],
    weakAgainst: ['<PERSON>', 'Dark', 'Steel']
  },
  Dragon: {
    strongAgainst: ['Dragon'],
    weakAgainst: ['Ice', 'Steel']
  },
  Electric: {
    strongAgainst: ['Water', 'Flying'],
    weakAgainst: ['Ground']
  },
  Fairy: {
    strongAgainst: ['Fighting', 'Dark', 'Dragon'],
    weakAgainst: ['Poison', 'Steel']
  },
  Fighting: {
    strongAgainst: ['Normal', 'Ice', 'Rock', 'Dark', 'Steel'],
    weakAgainst: ['Flying', 'Psychic', 'Fairy']
  },
  Fire: {
    strongAgainst: ['Grass', 'Ice', 'Bug', 'Steel'],
    weakAgainst: ['Water', 'Ground', 'Rock']
  },
  Flying: {
    strongAgainst: ['Grass', 'Fighting', 'Bug'],
    weakAgainst: ['Electric', 'Rock', 'Steel']
  },
  Ghost: {
    strongAgainst: ['Ghost', 'Psychic'],
    weakAgainst: ['Dark', 'Ghost']
  },
  Grass: {
    strongAgainst: ['Water', 'Ground', 'Rock'],
    weakAgainst: ['Fire', 'Poison', 'Flying', 'Bug', 'Dragon', 'Steel']
  },
  Ground: {
    strongAgainst: ['Fire', 'Poison', 'Electric', 'Rock', 'Steel'],
    weakAgainst: ['Grass', 'Flying', 'Bug']
  },
  Ice: {
    strongAgainst: ['Grass', 'Ground', 'Flying', 'Dragon'],
    weakAgainst: ['Water', 'Ice', 'Fire', 'Steel']
  },
  Normal: {
    strongAgainst: [],
    weakAgainst: ['Fighting']
  },
  Poison: {
    strongAgainst: ['Grass'],
    weakAgainst: ['Ground']
  },
  Psychic: {
    strongAgainst: ['Fighting', 'Poison'],
    weakAgainst: ['Bug', 'Ghost', 'Dark']
  },
  Rock: {
    strongAgainst: ['Ice', 'Fire', 'Flying', 'Bug'],
    weakAgainst: ['Fighting', 'Grass', 'Ground', 'Steel', 'Water']
  },
  Steel: {
    strongAgainst: ['Fairy', 'Ice', 'Rock'],
    weakAgainst: ['Fighting', 'Fire', 'Ground']
  },
  Water: {
    strongAgainst: ['Fire', 'Ground', 'Rock'],
    weakAgainst: ['Electric', 'Grass']
  }
};

export const getTypeEffectiveness = (pokemonType) => {
  const normalizedType = pokemonType.charAt(0).toUpperCase() + pokemonType.slice(1).toLowerCase();
  return typeEffectiveness[normalizedType] || { strongAgainst: [], weakAgainst: [] };
}; 