import './App.css';
import React from 'react';
import { useRoutes } from 'react-router-dom'
import SeePokemons from './pages/seePokemons' 
import CreatePokemon from './pages/createPokemon'
import EditPokemon from './pages/editPokemon'
import PokemonDetails from './pages/PokemonDetails'
import Home from './pages/Home'
import Sidebar from './components/Sidebar'

const App = () => {
  
  let element = useRoutes([
    {
      path: "/",
      element: <Home />
    },
    {
      path: "/pokemons",
      element: <SeePokemons />
    },
    {
      path: "/pokemon/:id",
      element: <PokemonDetails />
    },
    {
      path:"/edit/:id",
      element: <EditPokemon />
    },
    {
      path:"/new",
      element: <CreatePokemon />
    }
  ]);

  return ( 
    <div className="App">
      <Sidebar />
      <div className="main-content">
        {element}
      </div>
    </div>
  )
}

export default App
