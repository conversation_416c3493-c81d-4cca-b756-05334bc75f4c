# Web Development Project 7 - *PokeMates*

Submitted by: **Anubhav**

This web app: **PokeMates**, allows users to create, view, edit, and delete custom Pokémon-inspired crewmates. Users can assign names and attributes to each crewmate, view a summary of all their created crewmates sorted by creation date, and access detailed info pages for each one. The app provides an intuitive interface for managing your collection, with features for updating and removing crewmates, as well as direct links to individual crewmate detail pages.

Time spent: **9** hours spent in total

## Required Features

The following **required** functionality is completed:


- [x] **The web app contains a page that features a create form to add a new crewmate**
  - Users can name the crewmate
  - Users can set the crewmate’s attributes by clicking on one of several values
- [x] **The web app includes a summary page of all the user’s added crewmatese**
  -  The web app contains a summary page dedicated to displaying all the crewmates the user has made so far
  -  The summary page is sorted by creation date such that the most recently created crewmates appear at the top
- [x] **A previously created crewmate can be updated from the list of crewmates in the summary page**
  - Each crewmate has an edit button that will take users to an update form for the relevant crewmate
  - Users can see the current attributes of their crewmate on the update form
  - After editing the crewmate's attribute values using the form, the user can immediately see those changes reflected in the update form and on the summary page 
- [x] **A previously created crewmate can be deleted from the crewmate list**
  - Using the edit form detailed in the previous _crewmates can be updated_ feature, there is a button that allows users to delete that crewmate
  - After deleting a crewmate, the crewmate should no longer be visible in the summary page
  - [x] **Each crewmate has a direct, unique URL link to an info page about them**
    - Clicking on a crewmate in the summary page navigates to a detail page for that crewmate
    - The detail page contains extra information about the crewmate not included in the summary page
    - Users can navigate to to the edit form from the detail page



The following **additional** features are implemented:
- [x] Extra information regarding Pokemons Added is given: specifically what types the Pokemons are good/bad against.

## Video Walkthrough

Here's a walkthrough of implemented user stories:

<img src='src/assets/HQ-week7DemoHighQualiyGIF.gif' title='Video Walkthrough' width='' alt='Video Walkthrough' />


GIF created with ...  
MacOS Screen Record Tool

## Notes

- Working with Supabase was fun yet challenging, a lot of mistakes made and a lot of time spent debugging, specifically on why tables didn't update properly. 


## License

    Copyright [2025] [Anubhav Dhungana]

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.